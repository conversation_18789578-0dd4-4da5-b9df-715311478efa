<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" integrity="sha384-wEmeIV1mKuiNpC+IOBjI7aAzPcEZeedi5yW5f2yOq55WWLwNGmvvx4Um1vskeMj0" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- fav icon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">


    <style>

        /* Bootstrap 5.0.0 compatible styles */
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif !important;
            background-color: #cee3e0 !important;
        }

        /* Ensure all text elements use Comfortaa font */
        h1, h2, h3, h4, h5, h6, p, ul, li, strong, em, b, s, small, span {
            font-family: 'Comfortaa', sans-serif !important;
        }

        /* Enhanced card styling for Bootstrap 5.0.0 */
        .card-primary {
            background: linear-gradient(135deg, #1572E8 0%, #0d5aa7 100%);
            color: white;
            border-radius: 1rem !important;
            border: none;
            box-shadow: 0 4px 20px rgba(21, 114, 232, 0.3);
            transition: all 0.3s ease;
        }

        .card-info {
            background: linear-gradient(135deg, #48ABF7 0%, #2980b9 100%);
            color: white;
            border-radius: 1rem !important;
            border: none;
            box-shadow: 0 4px 20px rgba(72, 171, 247, 0.3);
            transition: all 0.3s ease;
        }

        .card-success {
            background: linear-gradient(135deg, #31CE36 0%, #27ae60 100%);
            color: white;
            border-radius: 1rem !important;
            border: none;
            box-shadow: 0 4px 20px rgba(49, 206, 54, 0.3);
            transition: all 0.3s ease;
        }

        .card-secondary {
            background: linear-gradient(135deg, #6861CE 0%, #5a4fcf 100%);
            color: white;
            border-radius: 1rem !important;
            border: none;
            box-shadow: 0 4px 20px rgba(104, 97, 206, 0.3);
            transition: all 0.3s ease;
        }

        /* Card hover effects */
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2) !important;
        }

        .icon-big {
            font-size: 3rem;
        }

        .card-round {
            border-radius: 1rem !important;
        }

        /* Enhanced card body */
        .card-body {
            padding: 1.5rem;
        }

        .card-title {
            font-weight: 700;
            font-size: 1.8rem;
        }

        .card-category {
            font-weight: 500;
            opacity: 0.9;
            font-size: 0.9rem;
        }


        /* Sidebar styles are handled by sidebar.html - removed redundant CSS */


        .main-content {
            margin-left: 250px;
            padding: 4px;
            transition: all 0.3s;
        }

        /* .navbar {
            transition: all 0.3s;
            margin-bottom: 20px;
        } */
        .navbar {
            position: -webkit-sticky;
            position: sticky;
            top: 0;
            background-color: #f0f0f0;
            box-shadow: 0px 4px 6px 1px rgba(150,142,150,1);
        }

        .nav-item a img {
            width: 25px;
            padding-bottom: 4px;
        }

        #dropdown-menu {
            width: 350px;
            padding: 20px;

        }

        hr {
            width: 100%;
            color: black;
            margin-bottom: 0px;
        }

        .viewbtn {
            background-color: #050505;
            width: 90px;
            font-size: 11px;
            color: #fff;
            margin-left: 110px;
            padding-top: 10px;
        }

        .viewbtn:hover {
            background-color: white;
            color: black;
            border: 1px solid black;
        }


        .profile-img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            /* margin-bottom: 15px; */
            border-radius: 50%;
            margin-left: 10px;
        }



        .form-control:focus {
            /* border: none;
            outline: none; */
            box-shadow: none;
        }

        /* Footer Menu */
        .footer-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            background-color: #f1f8ff;
            padding: 10px 0;
            z-index: 1000;
        }

        .footer-menu a,
        .footer-menu .settings-link {
            color: #000000;
            font-size: 24px;
            text-align: center;
            text-decoration: none;
            position: relative;
        }

        .footer-menu a.active i,
        .footer-menu .settings-link.active i {
            color: #020202;
        }

        .footer-menu .settings-link .submenu {
            display: none;
            position: absolute;
            bottom: 65px;
            left: -250px;
            background-color: #ffffff;
            border: 1px solid #000000;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.226);
            padding: 10px;
            z-index: 1001;
            white-space: nowrap;
        }

        .footer-menu .settings-link a {
            display: block;
            color: #000000;
            padding: 5px 10px;
            text-decoration: none;
            text-align: start;
            margin-top: 10px;
        }

        .footer-menu .settings-link a:hover {
            background-color: #000000;
            color: white;
            border-radius: 3px;
        }

        .footer-menu .settings-link.active {
            display: block;
        }

        .submenu {
            display: none;
            position: absolute;
            background-color: #fff;
            box-shadow: 0 0 10px rgb(192, 221, 253);
            z-index: 1000;
            left: 0;
            margin-top: 5px;
            padding: 10px;
            height: auto;
            width: 310px;
        }

        .sub-submenu {
            display: none;
            position: absolute;
            left: 100%;
            /* Position sub-submenu to the right of submenu */
            top: 0;
            margin-top: -10px;
            /* Adjust top margin as needed */
            padding: 5px 0;
            /* Add padding to sub-submenu */
            background-color: #f9f9f9;
            /* Adjust background color */
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.226);
            /* Optional: Add shadow for better visibility */
        }

        .settings-link:hover .submenu,
        .settings-link:focus .submenu {
            display: block;
        }

        .sub-submenu {
            display: none;
        }

        .submenu-item:hover .sub-submenu,
        .submenu-item:focus .sub-submenu {
            display: block;
        }

        /* Enhanced button styling for Bootstrap 5.0.0 */
        .btn {
            border-radius: 1rem !important;
            transition: all 0.2s ease;
            font-weight: 600;
        }

        .btn-success {
            background-color: white;
            color: #000000;
            border: 1px solid #198754;
        }

        .btn-success:hover {
            background-color: #198754;
            color: white;
            border-color: #198754;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(25, 135, 84, 0.3);
        }

        .btn-danger {
            background-color: white;
            color: rgb(0, 0, 0);
            border: 1px solid #dc3545;
        }

        .btn-danger:hover {
            background-color: #dc3545;
            color: rgb(255, 255, 255);
            border-color: #dc3545;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        .btn-link {
            color: #000000;
            padding-left: 2px;
            margin-bottom: -12px;
        }

        .btn-link:hover {
            color: #000000;
            text-decoration: none;
        }

        .submenu a {
            display: block;
            padding: 8px 12px;
            text-decoration: none;
            color: #333;
            /* Example text color */
        }

        .submenu a:hover {
            background-color: #f0f0f0;
            /* Example hover background color */
        }

        .footer {
            /* background-color: #dad9df; */
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;

        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;

        }

        /*
        .footer p {
            font-size: 12px;
            padding-top: 5px;
        } */

        @media (max-width:768px) {
            .footer {
                margin-bottom: 50px;
            }

            .footer img {
                width: 60%;
                padding-bottom: 60px;
            }

        }

        #notificationBtn {
            display: none;
        }
        .icon-text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .icon-text-wrapper i {
            font-size: 24px;
            /* Adjust the icon size as needed */
            margin-bottom: 5px;
            /* Space between icon and text */
        }

        .dashboard-text {
            font-size: 12px;
            /* Match the font size to the icon size */
            line-height: 24px;
            /* Set the line-height to match the icon's height */
        }

        @media only screen and (max-width: 767px) {
            .small_p p{
            font-size: 12px !important;
        }
        }

        .analitics_container p {
            font-size: 1rem;
        }

        .analitics_container i {
            font-size: 1.9rem;
        }

        .col-stats {
            padding: 0;
            margin: 0;
        }

    </style>
</head>

<body>


    {% include "sidebar.html" %}

    <br>
        <div class="d-flex justify-content-center">
            <div class="alert alert-success text-center" role="alert" style="width: 20rem;">
               DASHBOARD / ANALYTICS
            </div>
        </div>

    <div class="container-fluid analitics_container">

        <h3 class="fw-bold mb-3">Student & Visitor Data</h3>
        <div class="row">
            <div class="col-sm-6 col-md-3 mb-3">
                <div class="card card-stats card-primary card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Total Student</p>
                                    <h4 class="card-title">{{total_student_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-3 mb-3">
                <div class="card card-stats card-info card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-person"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Male</p>
                                    <h4 class="card-title">{{male_student_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-3 mb-3">
                <div class="card card-stats card-success card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-person-dress"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Female</p>
                                    <h4 class="card-title">{{female_student_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-3 mb-3">
                <div class="card card-stats card-secondary card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Students ({% now "F Y" %}) </p>
                                    <h4 class="card-title"> {{monthly_counts}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-md-3 mt-3">
                <div class="card card-stats bg-secondary text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Total Visitor</p>
                                    <h4 class="card-title">{{total_visitors_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="col-sm-6 col-md-3 mt-3">
                <div class="card card-stats bg-warning text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-clock"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Pending Visitors</p>
                                    <h4 class="card-title">{{pending_visitors_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <!-- <div class="col-sm-6 col-md-3 mt-3">
                <div class="card card-stats bg-danger text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-person-dress"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Completed Visitors</p>
                                    <h4 class="card-title">{{completed_visitors_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <!-- <div class="col-sm-6 col-md-3 mt-3">
                <div class="card card-stats bg-secondary text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Cancled Visitors</p>
                                    <h4 class="card-title">{{cancelled_visitors_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
        </div>
    </div>

    <div class="container-fluid mt-5 mb-4 analitics_container">
        <h3 class="fw-bold mb-3">Financial Analytics Data</h3>
        <div class="row">
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats card-primary card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-coins"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Y'Day Closing Balance</p>
                                    <h4 class="card-title">{{yesterday_closing_balance}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-success text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-indian-rupee-sign"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Daily Collection</p>
                                    <h4 class="card-title">{{daily_total_balance}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <!-- <div class="card card-stats bg-warning text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="bi bi-alarm-fill"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Due Collectable</p>
                                    <h4 class="card-title">{{total_amount_due}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
            </div>
            {% comment %} <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats card-secondary card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="bi bi-cash-coin"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Monthly Total Collection</p>
                                    <h4 class="card-title">123</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-danger text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="bi bi-graph-up-arrow"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Monthly Profit & loss</p>
                                    <h4 class="card-title">1,294</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-warning text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-clock"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Extra</p>
                                    <h4 class="card-title">100</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-success text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-chart-pie"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Quarterly Total Collection</p>
                                    <h4 class="card-title">100</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats card-primary card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="bi bi-graph-up-arrow"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Quarterly Profit & Loss</p>
                                    <h4 class="card-title">123</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-secondary text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Total Visitor</p>
                                    <h4 class="card-title">123</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats card-secondary text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-chart-pie"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Half Yr. Total Collection</p>
                                    <h4 class="card-title">123</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-danger text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="bi bi-graph-up-arrow"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Half Yr. Profit & Loss</p>
                                    <h4 class="card-title">123</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-secondary text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Total Visitor</p>
                                    <h4 class="card-title">123</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> {% endcomment %}
        </div>
        <!-- footer -->
        <div class="footer">
            <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">
            <!-- <p>Developed with passion by Librainian</p> -->
        </div>
    </div>





    <!-- JavaScript dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap5.min.js"></script>
    <!-- Bootstrap 5.0.0 JavaScript Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-p34f1UUtsS3wqzfto5wAAmdvj+osOnFyQFpp4Ua3gs/ZVWx6oOypYoCJhGGScy+8" crossorigin="anonymous"></script>

    <!-- Bootstrap 5.0.0 initialization check -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof bootstrap !== 'undefined') {
                console.log('Bootstrap 5.0.0 loaded successfully for analytics page');
                console.log('Bootstrap version:', bootstrap.Modal.VERSION || 'Version not available');
            } else {
                console.error('Bootstrap is not loaded!');
            }
        });
    </script>

    <!-- Consolidated JavaScript for better performance -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // User dropdown functionality (if elements exist)
            const userDropdown = document.getElementById("userDropdown");
            const userDropdownContent = document.getElementById("userDropdownContent");

            if (userDropdown && userDropdownContent) {
                userDropdown.addEventListener("click", function () {
                    userDropdownContent.classList.toggle("show");
                });

                // Close the dropdown if the user clicks outside of it
                window.addEventListener("click", function (event) {
                    if (!event.target.matches("#userDropdown")) {
                        var dropdowns = document.getElementsByClassName("dropdown-content");
                        for (var i = 0; i < dropdowns.length; i++) {
                            var openDropdown = dropdowns[i];
                            if (openDropdown.classList.contains("show")) {
                                openDropdown.classList.remove("show");
                            }
                        }
                    }
                });
            }

            // Menu icon functionality (if elements exist)
            const menuIcon = document.getElementById('menu-icon');
            const submenu = document.getElementById('submenu');

            if (menuIcon && submenu) {
                menuIcon.addEventListener('click', function () {
                    submenu.classList.toggle('show');
                });
            }

            // Footer search modal functionality (if elements exist)
            const footerSearch = document.getElementById('footer-search');
            const exampleModal = document.getElementById('exampleModal');

            if (footerSearch && exampleModal) {
                const modalInstance = new bootstrap.Modal(exampleModal);
                footerSearch.addEventListener('click', function () {
                    modalInstance.show();
                });
            }
        });
    </script>
    <script>
        function updateDateTime() {
            const now = new Date();
            const date = now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
            const time = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

            document.getElementById('date').textContent = 'Date: ' + date;
            document.getElementById('time').textContent = 'Time: ' + time;
        }

        // Update the date and time on page load
        updateDateTime();

        // Update the date and time every minute
        setInterval(updateDateTime, 60000);
    </script>

</body>

</html>